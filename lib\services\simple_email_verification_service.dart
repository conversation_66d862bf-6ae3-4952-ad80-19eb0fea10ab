import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';
import '../utils/logger.dart';
import 'real_email_service.dart';

/// خدمة التحقق من البريد الإلكتروني المبسطة - تتجاوز Firebase Auth
class SimpleEmailVerificationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collectionName = 'verification_codes';
  static const Duration _codeExpiration = Duration(minutes: 10);

  /// إنشاء كود تحقق عشوائي
  static String generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// تشفير الكود
  static String _hashCode(String code) {
    final bytes = utf8.encode(code);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// إرسال كود التحقق (نظام مبسط مع إرسال إيميل حقيقي)
  static Future<String?> sendVerificationCode(String email) async {
    try {
      AppLogger.info(
        '🔄 إنشاء وإرسال كود التحقق للبريد: $email',
        'SimpleEmailVerification',
      );

      final code = generateVerificationCode();

      // محاولة حفظ الكود في Firestore (اختياري)
      final saved = await _saveVerificationCode(email, code);
      if (!saved) {
        AppLogger.warning(
          '⚠️ فشل حفظ الكود في Firestore، سيتم استخدام التخزين المحلي',
          'SimpleEmailVerification',
        );
        // حفظ الكود محلياً كبديل
        await _saveCodeLocally(email, code);
      }

      // محاولة إرسال الإيميل الحقيقي
      AppLogger.info('📧 محاولة إرسال الإيميل...', 'SimpleEmailVerification');
      final emailSent = await _sendRealEmail(email, code);

      if (emailSent) {
        AppLogger.success(
          '✅ تم إرسال كود التحقق عبر الإيميل بنجاح',
          'SimpleEmailVerification',
        );
        // عرض الكود أيضاً للتطوير
        _displayCodeForTesting(email, code);
        return code;
      } else {
        AppLogger.warning(
          '⚠️ فشل إرسال الإيميل - إعدادات البريد الإلكتروني غير مُكونة',
          'SimpleEmailVerification',
        );
        AppLogger.info(
          '📋 يرجى مراجعة دليل الإعداد: EMAIL_VERIFICATION_FIX_GUIDE.md',
          'SimpleEmailVerification',
        );
        // إذا فشل الإرسال، اعرض الكود للمستخدم
        _displayCodeForTesting(email, code);
        return code;
      }
    } catch (e) {
      AppLogger.error(
        '❌ خطأ في إرسال كود التحقق',
        'SimpleEmailVerification',
        e,
      );
      return null;
    }
  }

  /// حفظ كود التحقق في Firestore
  static Future<bool> _saveVerificationCode(String email, String code) async {
    try {
      final hashedCode = _hashCode(code);
      final expiresAt = DateTime.now().add(_codeExpiration);

      // تنظيف البريد الإلكتروني لاستخدامه كـ document ID
      final cleanEmail = email.replaceAll('.', '_dot_').replaceAll('@', '_at_');

      await _firestore.collection(_collectionName).doc(cleanEmail).set({
        'hashedCode': hashedCode,
        'expiresAt': Timestamp.fromDate(expiresAt),
        'attempts': 0,
        'createdAt': Timestamp.fromDate(DateTime.now()),
        'isUsed': false,
        'email': email,
        'originalCode': code, // إضافة الكود الأصلي للمراجعة (مؤقت)
      });

      AppLogger.success(
        '✅ تم حفظ كود التحقق في Firestore',
        'SimpleEmailVerification',
      );
      return true;
    } catch (e) {
      AppLogger.error(
        '❌ فشل في حفظ كود التحقق في Firestore',
        'SimpleEmailVerification',
        e,
      );
      return false;
    }
  }

  /// التحقق من صحة الكود
  static Future<bool> verifyCode(String email, String code) async {
    try {
      AppLogger.info(
        '🔍 التحقق من كود: $code للبريد: $email',
        'SimpleEmailVerification',
      );

      // محاولة التحقق من Firestore أولاً
      try {
        final cleanEmail = email
            .replaceAll('.', '_dot_')
            .replaceAll('@', '_at_');
        final doc =
            await _firestore.collection(_collectionName).doc(cleanEmail).get();

        if (doc.exists) {
          // التحقق من Firestore
          final data = doc.data()!;
          final hashedCode = data['hashedCode'] as String;
          final expiresAt = (data['expiresAt'] as Timestamp).toDate();
          final attempts = data['attempts'] as int;
          final isUsed = data['isUsed'] as bool? ?? false;

          // التحقق من انتهاء الصلاحية
          if (DateTime.now().isAfter(expiresAt)) {
            AppLogger.warning(
              '⚠️ انتهت صلاحية كود التحقق',
              'SimpleEmailVerification',
            );
            return false;
          }

          // التحقق من الاستخدام السابق
          if (isUsed) {
            AppLogger.warning(
              '⚠️ تم استخدام هذا الكود من قبل',
              'SimpleEmailVerification',
            );
            return false;
          }

          // التحقق من عدد المحاولات (5 محاولات فقط لنفس الكود)
          if (attempts >= 5) {
            AppLogger.warning(
              '⚠️ تم تجاوز عدد المحاولات المسموح لهذا الكود (5 محاولات)',
              'SimpleEmailVerification',
            );
            return false;
          }

          // التحقق من صحة الكود
          final inputHashedCode = _hashCode(code);
          if (inputHashedCode == hashedCode) {
            // تحديث الكود كمستخدم
            await _firestore.collection(_collectionName).doc(email).update({
              'isUsed': true,
              'usedAt': Timestamp.fromDate(DateTime.now()),
            });

            AppLogger.success(
              '✅ تم التحقق من الكود بنجاح',
              'SimpleEmailVerification',
            );
            return true;
          } else {
            // زيادة عدد المحاولات
            await _firestore.collection(_collectionName).doc(email).update({
              'attempts': attempts + 1,
            });

            AppLogger.warning(
              '⚠️ كود التحقق غير صحيح',
              'SimpleEmailVerification',
            );
            return false;
          }
        }
      } catch (e) {
        AppLogger.warning(
          '⚠️ فشل الوصول لـ Firestore، سيتم التحقق محلياً',
          'SimpleEmailVerification',
        );
      }

      // إذا فشل Firestore، استخدم التحقق المحلي
      AppLogger.info('🔍 التحقق من الكود المحلي...', 'SimpleEmailVerification');
      return await _verifyCodeLocally(email, code);
    } catch (e) {
      AppLogger.error('❌ خطأ في التحقق من الكود', 'SimpleEmailVerification', e);
      return false;
    }
  }

  /// حذف كود التحقق بعد الاستخدام
  static Future<void> deleteVerificationCode(String email) async {
    try {
      await _firestore.collection(_collectionName).doc(email).delete();
      AppLogger.info('🗑️ تم حذف كود التحقق', 'SimpleEmailVerification');
    } catch (e) {
      AppLogger.warning('⚠️ فشل في حذف كود التحقق', 'SimpleEmailVerification');
    }
  }

  /// عرض الكود للاختبار
  static void _displayCodeForTesting(String email, String code) {
    // استخدام AppLogger.displayVerificationCode للعرض الجميل
    AppLogger.displayVerificationCode(email, code);

    // عرض إضافي مبسط
    AppLogger.info('', 'SimpleEmailVerification');
    AppLogger.info(
      '🎯 ═══════════════════════════════════════',
      'SimpleEmailVerification',
    );
    AppLogger.info('🎯 كود التحقق للاختبار', 'SimpleEmailVerification');
    AppLogger.info(
      '🎯 ═══════════════════════════════════════',
      'SimpleEmailVerification',
    );
    AppLogger.info('📧 البريد الإلكتروني: $email', 'SimpleEmailVerification');
    AppLogger.info('🔢 كود التحقق: $code', 'SimpleEmailVerification');
    AppLogger.info(
      '⏰ صالح لمدة: ${_codeExpiration.inMinutes} دقائق',
      'SimpleEmailVerification',
    );
    AppLogger.info(
      '🎯 ═══════════════════════════════════════',
      'SimpleEmailVerification',
    );
    AppLogger.info('', 'SimpleEmailVerification');
  }

  /// التحقق من وجود كود صالح
  static Future<bool> hasValidCode(String email) async {
    try {
      final doc = await _firestore.collection(_collectionName).doc(email).get();

      if (!doc.exists) return false;

      final data = doc.data()!;
      final expiresAt = (data['expiresAt'] as Timestamp).toDate();
      final isUsed = data['isUsed'] as bool? ?? false;

      return !isUsed && DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }

  /// التحقق من تجاوز الحد اليومي (200 كود يومياً)
  static Future<bool> _hasExceededDailyLimit(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().substring(
        0,
        10,
      ); // YYYY-MM-DD
      final key = 'daily_codes_${email}_$today';
      final attempts = prefs.getInt(key) ?? 0;

      return attempts >= 200;
    } catch (e) {
      return false;
    }
  }

  /// تسجيل محاولة إرسال يومية
  static Future<void> _recordDailyAttempt(String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().substring(0, 10);
      final key = 'daily_codes_${email}_$today';
      final attempts = prefs.getInt(key) ?? 0;
      await prefs.setInt(key, attempts + 1);

      AppLogger.info(
        '📊 عدد الكودات المرسلة اليوم: ${attempts + 1}/200',
        'SimpleEmailVerification',
      );
    } catch (e) {
      // تجاهل أخطاء التسجيل
    }
  }

  /// إعادة إرسال كود التحقق (بدون حدود)
  static Future<String?> resendVerificationCode(String email) async {
    AppLogger.info(
      '🔄 إعادة إرسال كود التحقق للبريد: $email',
      'SimpleEmailVerification',
    );

    // حذف الكود القديم أولاً
    await deleteVerificationCode(email);

    // إرسال كود جديد
    return await sendVerificationCode(email);
  }

  /// إرسال الإيميل الحقيقي
  static Future<bool> _sendRealEmail(String email, String code) async {
    try {
      // محاولة إرسال عبر RealEmailService (يتضمن Formspree الآن)
      final result = await RealEmailService.sendVerificationCode(email, code);
      if (result) {
        AppLogger.success(
          '✅ تم إرسال الإيميل بنجاح',
          'SimpleEmailVerification',
        );
        return true;
      } else {
        AppLogger.warning(
          '⚠️ فشل إرسال الإيميل، سيتم عرض الكود محلياً',
          'SimpleEmailVerification',
        );
        return false;
      }
    } catch (e) {
      AppLogger.error(
        '❌ خطأ في إرسال الإيميل الحقيقي',
        'SimpleEmailVerification',
        e,
      );
      return false;
    }
  }

  /// حفظ الكود محلياً كبديل
  static Future<void> _saveCodeLocally(String email, String code) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hashedCode = _hashCode(code);
      final expiresAt =
          DateTime.now().add(_codeExpiration).millisecondsSinceEpoch;

      await prefs.setString('verification_code_$email', hashedCode);
      await prefs.setInt('verification_expiry_$email', expiresAt);
      await prefs.setInt('verification_attempts_$email', 0);

      AppLogger.success('✅ تم حفظ الكود محلياً', 'SimpleEmailVerification');
    } catch (e) {
      AppLogger.error(
        '❌ فشل في حفظ الكود محلياً',
        'SimpleEmailVerification',
        e,
      );
    }
  }

  /// التحقق من الكود المحفوظ محلياً
  static Future<bool> _verifyCodeLocally(String email, String code) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedHashedCode = prefs.getString('verification_code_$email');
      final expiryTime = prefs.getInt('verification_expiry_$email');
      final attempts = prefs.getInt('verification_attempts_$email') ?? 0;

      if (savedHashedCode == null || expiryTime == null) {
        return false;
      }

      // التحقق من انتهاء الصلاحية
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        AppLogger.warning(
          '⚠️ انتهت صلاحية الكود المحلي',
          'SimpleEmailVerification',
        );
        return false;
      }

      // التحقق من عدد المحاولات
      if (attempts >= 5) {
        AppLogger.warning(
          '⚠️ تم تجاوز عدد المحاولات المحلية',
          'SimpleEmailVerification',
        );
        return false;
      }

      // التحقق من صحة الكود
      final inputHashedCode = _hashCode(code);
      if (inputHashedCode == savedHashedCode) {
        // حذف الكود بعد الاستخدام
        await prefs.remove('verification_code_$email');
        await prefs.remove('verification_expiry_$email');
        await prefs.remove('verification_attempts_$email');

        AppLogger.success(
          '✅ تم التحقق من الكود المحلي بنجاح',
          'SimpleEmailVerification',
        );
        return true;
      } else {
        // زيادة عدد المحاولات
        await prefs.setInt('verification_attempts_$email', attempts + 1);
        AppLogger.warning(
          '⚠️ كود التحقق المحلي غير صحيح',
          'SimpleEmailVerification',
        );
        return false;
      }
    } catch (e) {
      AppLogger.error(
        '❌ خطأ في التحقق من الكود المحلي',
        'SimpleEmailVerification',
        e,
      );
      return false;
    }
  }
}
