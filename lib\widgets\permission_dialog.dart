import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/permission_service.dart';
import '../theme/app_theme.dart';

/// Dialog لطلب أذونات التخزين
class PermissionDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onGranted;
  final VoidCallback? onDenied;

  const PermissionDialog({
    super.key,
    required this.title,
    required this.message,
    this.onGranted,
    this.onDenied,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.security,
            color: AppTheme.primaryColor,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textColor,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: AppTheme.textColor.withOpacity(0.8),
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.primaryColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'هذه الأذونات ضرورية لحفظ ملفات PDF على جهازك',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onDenied?.call();
          },
          child: Text(
            'إلغاء',
            style: GoogleFonts.cairo(
              color: Colors.grey,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.of(context).pop();
            
            final permissionService = PermissionService();
            final granted = await permissionService.requestStoragePermissions();
            
            if (granted) {
              onGranted?.call();
            } else {
              onDenied?.call();
              
              // إظهار dialog للذهاب للإعدادات
              if (context.mounted) {
                _showSettingsDialog(context);
              }
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(
            'منح الأذونات',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// إظهار dialog للذهاب للإعدادات
  static void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'أذونات مطلوبة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textColor,
          ),
        ),
        content: Text(
          'لم يتم منح الأذونات المطلوبة. يمكنك منحها من إعدادات التطبيق.',
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: AppTheme.textColor.withOpacity(0.8),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                color: Colors.grey,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final permissionService = PermissionService();
              await permissionService.openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'فتح الإعدادات',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// إظهار dialog طلب الأذونات
  static Future<bool> show(
    BuildContext context, {
    String? title,
    String? message,
  }) async {
    bool granted = false;
    
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PermissionDialog(
        title: title ?? 'أذونات التخزين',
        message: message ?? PermissionService().getPermissionMessage(),
        onGranted: () => granted = true,
        onDenied: () => granted = false,
      ),
    );
    
    return granted;
  }
}
